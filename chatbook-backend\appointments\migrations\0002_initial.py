# Generated by Django 4.2.22 on 2025-08-07 08:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('appointments', '0001_initial'),
        ('employees', '0001_initial'),
        ('services', '0001_initial'),
        ('business', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointmentservice',
            name='employee_service',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointment_services', to='services.employeeservice'),
        ),
        migrations.AddField(
            model_name='appointmentservice',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='appointment_services', to='services.service'),
        ),
        migrations.AddField(
            model_name='appointmenthistory',
            name='appointment',
            field=models.ForeignKey(help_text='The appointment this history entry relates to', on_delete=django.db.models.deletion.CASCADE, related_name='history', to='appointments.appointment'),
        ),
        migrations.AddField(
            model_name='appointmentaddon',
            name='add_on',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointment_add_ons', to='services.addon'),
        ),
        migrations.AddField(
            model_name='appointmentaddon',
            name='appointment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointment_add_ons', to='appointments.appointment'),
        ),
        migrations.AddField(
            model_name='appointmentaddon',
            name='service_add_on',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointment_add_ons', to='services.serviceaddon'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='appointments', to='business.businesscustomer'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='appointments', to='employees.employee'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='recurrence',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='appointments.recurringpattern'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='services',
            field=models.ManyToManyField(related_name='appointments', through='appointments.AppointmentService', to='services.service'),
        ),
        migrations.AddIndex(
            model_name='appointmenthistory',
            index=models.Index(fields=['appointment', '-modified_at'], name='appt_history_appt_time_idx'),
        ),
        migrations.AddIndex(
            model_name='appointmenthistory',
            index=models.Index(fields=['action', '-modified_at'], name='appt_history_action_time_idx'),
        ),
        migrations.AddIndex(
            model_name='appointmenthistory',
            index=models.Index(fields=['modified_by', '-modified_at'], name='appt_history_user_time_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='appointmentaddon',
            unique_together={('appointment', 'add_on')},
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['start_time'], name='appointment_start_time_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['employee', 'start_time'], name='appointment_employee_time_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['customer', 'start_time'], name='appt_customer_time_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['status'], name='appointment_status_idx'),
        ),
    ]
