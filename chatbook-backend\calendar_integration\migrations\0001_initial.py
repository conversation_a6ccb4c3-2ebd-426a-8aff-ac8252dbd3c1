# Generated by Django 4.2.22 on 2025-08-07 08:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CalendarConnection',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('provider', models.CharField(choices=[('google', 'Google Calendar'), ('outlook', 'Microsoft Outlook'), ('apple', 'Apple iCloud Calendar')], db_index=True, max_length=20)),
                ('provider_calendar_id', models.CharField(default='primary', help_text="Calendar ID from the provider (e.g., 'primary' for Google)", max_length=255)),
                ('calendar_name', models.Char<PERSON>ield(blank=True, help_text='Display name of the calendar', max_length=255)),
                ('connection_status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('error', 'Error'), ('expired', 'Token Expired')], db_index=True, default='active', max_length=20)),
                ('last_sync_at', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('last_sync_status', models.CharField(blank=True, max_length=50)),
                ('sync_enabled', models.BooleanField(default=True)),
                ('last_error', models.TextField(blank=True)),
                ('error_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sync_settings', models.JSONField(blank=True, default=dict, help_text='Provider-specific sync settings')),
                ('social_account', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='calendar_connection', to='accounts.socialaccount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_connections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Calendar Connection',
                'verbose_name_plural': 'Calendar Connections',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CalendarEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('appointment_id', models.IntegerField(db_index=True)),
                ('external_event_id', models.CharField(help_text='Event ID from the external calendar provider', max_length=255)),
                ('external_calendar_id', models.CharField(default='primary', help_text='Calendar ID where the event was created', max_length=255)),
                ('last_synced_at', models.DateTimeField(auto_now=True)),
                ('sync_status', models.CharField(choices=[('synced', 'Synced'), ('pending', 'Pending'), ('failed', 'Failed'), ('deleted', 'Deleted')], default='synced', max_length=20)),
                ('event_data_hash', models.CharField(help_text='Hash of event data to detect changes', max_length=64)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('calendar_connection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='synced_events', to='calendar_integration.calendarconnection')),
            ],
            options={
                'verbose_name': 'Calendar Event',
                'verbose_name_plural': 'Calendar Events',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['appointment_id'], name='cal_appointment_idx'), models.Index(fields=['external_event_id'], name='cal_external_event_idx'), models.Index(fields=['sync_status'], name='cal_sync_status_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='calendarevent',
            constraint=models.UniqueConstraint(fields=('appointment_id', 'calendar_connection'), name='unique_appointment_calendar'),
        ),
        migrations.AddConstraint(
            model_name='calendarevent',
            constraint=models.UniqueConstraint(fields=('external_event_id', 'calendar_connection'), name='unique_external_event_calendar'),
        ),
        migrations.AddIndex(
            model_name='calendarconnection',
            index=models.Index(fields=['user', 'connection_status'], name='cal_user_status_idx'),
        ),
        migrations.AddIndex(
            model_name='calendarconnection',
            index=models.Index(fields=['provider', 'connection_status'], name='cal_provider_status_idx'),
        ),
        migrations.AddIndex(
            model_name='calendarconnection',
            index=models.Index(fields=['last_sync_at'], name='cal_last_sync_idx'),
        ),
        migrations.AddConstraint(
            model_name='calendarconnection',
            constraint=models.UniqueConstraint(fields=('user', 'provider'), name='unique_user_provider_calendar'),
        ),
    ]
