# Generated by Django 4.2.22 on 2025-08-07 08:42

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import utils.timezone_validators


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccessLevel',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('level', models.IntegerField(default=0, help_text='Higher levels have more permissions')),
                ('permissions', models.JSONField(default=dict, help_text='JSON object defining permissions for this access level')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Access Level',
                'verbose_name_plural': 'Access Levels',
                'ordering': ['-level', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Business',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('website', models.URLField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
            ],
            options={
                'verbose_name': 'Business',
                'verbose_name_plural': 'Businesses',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BusinessSettings',
            fields=[
                ('business', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='business.business')),
            ],
            options={
                'verbose_name': 'Business Settings',
                'verbose_name_plural': 'Business Settings',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StylistLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the stylist level (e.g., Junior, Senior, Master)', max_length=100)),
                ('description', models.TextField(blank=True)),
                ('level_order', models.PositiveIntegerField(default=0, help_text='Order of this level (lower numbers = less experienced)')),
                ('is_default', models.BooleanField(default=False, help_text='Is this the default level for new employees?')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stylist_levels', to='business.business')),
            ],
            options={
                'verbose_name': 'Stylist Level',
                'verbose_name_plural': 'Stylist Levels',
                'ordering': ['business', 'level_order'],
            },
        ),
        migrations.CreateModel(
            name='SmartBookingRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rule_type', models.IntegerField(choices=[(1, 'Bookend Slots'), (2, 'Gapless Booking'), (3, 'Tentative Hold')], help_text='Type of smart booking rule')),
                ('is_enabled', models.BooleanField(default=False, help_text='Whether this rule is currently active')),
                ('tolerance_minutes', models.IntegerField(default=30, help_text='Tolerance threshold in minutes (used by Tentative Hold rule)', validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(120)])),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='smart_booking_rules', to='business.business')),
            ],
            options={
                'verbose_name': 'Smart booking rule',
                'verbose_name_plural': 'Smart booking rules',
                'ordering': ['rule_type'],
            },
        ),
        migrations.CreateModel(
            name='OnlineBookingRules',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timezone', models.CharField(default='UTC', help_text='Timezone for the business', max_length=50, validators=[utils.timezone_validators.validate_business_timezone])),
                ('currency', models.CharField(default='USD', help_text='Currency for pricing', max_length=3)),
                ('max_days_in_advance', models.IntegerField(default=30, help_text='Maximum days in advance a booking can be made', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)])),
                ('min_hours_before', models.IntegerField(default=24, help_text='Minimum hours before a booking can be made', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(168)])),
                ('appointment_interval', models.IntegerField(default=15, help_text='Interval between available online appointments (in minutes)', validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(60)])),
                ('allow_cancellation', models.BooleanField(default=True)),
                ('cancellation_hours_before', models.IntegerField(default=24)),
                ('cancellation_policy', models.TextField(blank=True, help_text='Detailed cancellation policy to show customers during the booking process', null=True)),
                ('allow_rescheduling', models.BooleanField(default=True)),
                ('rescheduling_hours_before', models.IntegerField(default=24)),
                ('require_payment', models.BooleanField(default=False)),
                ('deposit_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('enable_bookend_slots', models.BooleanField(default=False, help_text='Show only the first and last available time slots of the day per stylist')),
                ('enable_gapless_booking', models.BooleanField(default=False, help_text='Fill gaps in availability by snapping to actual appointment end times')),
                ('enable_tentative_hold', models.BooleanField(default=False, help_text='Show partial time slots when remaining time is within tolerance threshold')),
                ('tentative_hold_tolerance', models.IntegerField(default=30, help_text='Tolerance threshold in minutes for tentative hold bookings', validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(120)])),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('business', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='booking_rules', to='business.business')),
            ],
            options={
                'verbose_name': 'Online booking rules',
                'verbose_name_plural': 'Online booking rules',
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('address_line1', models.CharField(blank=True, max_length=255, null=True)),
                ('address_line2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='business.business')),
            ],
            options={
                'verbose_name': 'Location',
                'verbose_name_plural': 'Locations',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BusinessUser',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_primary', models.BooleanField(default=False, help_text='Primary users have full access to business settings and can manage other users')),
                ('custom_permissions', models.JSONField(default=dict, help_text='Additional custom permissions for this user, overriding access level defaults')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('access_level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='users', to='business.accesslevel')),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_users', to='business.business')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Business User',
                'verbose_name_plural': 'Business Users',
                'ordering': ['business', 'user'],
            },
        ),
        migrations.CreateModel(
            name='BusinessNotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('appointment_detail_enabled', models.BooleanField(default=True, help_text='Send notifications when an appointment is booked (Email, Text, Push)')),
                ('confirmation_request_enabled', models.BooleanField(default=True, help_text='Automatically send notifications asking customers to confirm they will show up (Email, Text, Push)')),
                ('confirmation_hours_before', models.IntegerField(default=72, help_text='Hours before appointment to send confirmation request', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)])),
                ('appointment_reminder_enabled', models.BooleanField(default=True, help_text='Automatically send notifications reminding customers about their appointment (Email, Text, Push)')),
                ('reminder_hours_before', models.IntegerField(default=24, help_text='Hours before appointment to send reminder', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)])),
                ('new_appointment_message', models.TextField(blank=True, help_text='Custom message to include in new appointment emails (max 1000 characters)', max_length=1000)),
                ('cancellation_no_show_policy_message', models.TextField(blank=True, help_text='Additional cancellation and no-show policy message to include in appointment emails (max 2000 characters)', max_length=2000)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('business', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to='business.business')),
            ],
            options={
                'verbose_name': 'Business Notification Settings',
                'verbose_name_plural': 'Business Notification Settings',
            },
        ),
        migrations.CreateModel(
            name='BusinessCustomer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, help_text='Employee notes about this customer')),
                ('loyalty_points', models.IntegerField(default=0)),
                ('opt_in_marketing', models.BooleanField(default=True)),
                ('email_reminders', models.BooleanField(default=True)),
                ('sms_reminders', models.BooleanField(default=True)),
                ('signed_forms', models.JSONField(default=list, help_text='List of form template IDs that this customer has signed')),
                ('forms_completed_at', models.DateTimeField(blank=True, help_text='Timestamp when all required forms were completed', null=True)),
                ('customer_since', models.CharField(blank=True, help_text='Original customer since date from import', max_length=100, null=True)),
                ('last_visited', models.CharField(blank=True, help_text='Last visit date from import', max_length=100, null=True)),
                ('membership_type', models.CharField(blank=True, help_text='Membership type from import', max_length=100, null=True)),
                ('referred_by', models.CharField(blank=True, help_text='Referral source from import', max_length=255, null=True)),
                ('online_booking_allowed', models.BooleanField(blank=True, help_text='Online booking preference from import', null=True)),
                ('credit_card_info', models.CharField(blank=True, help_text='Credit card info from import', max_length=255, null=True)),
                ('appointments_booked', models.IntegerField(blank=True, help_text='Total appointments from import', null=True)),
                ('classes_booked', models.IntegerField(blank=True, help_text='Total classes from import', null=True)),
                ('amount_paid', models.DecimalField(blank=True, decimal_places=2, help_text='Total amount paid from import', max_digits=10, null=True)),
                ('no_shows_cancellations', models.IntegerField(blank=True, help_text='No shows/cancellations from import', null=True)),
                ('employee_seen', models.TextField(blank=True, help_text='Employees seen from import', null=True)),
                ('imported_at', models.DateTimeField(blank=True, help_text='When this customer was imported', null=True)),
                ('import_source', models.CharField(blank=True, help_text='Source of import (filename, etc)', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_customers', to='business.business')),
            ],
            options={
                'verbose_name': 'Business customer',
                'verbose_name_plural': 'Business customers',
            },
        ),
    ]
