ERROR 2025-07-23 04:52:07,170 exceptions custom_exception_handler 104 Unhandled API Exception [b8266201]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,170 exceptions custom_exception_handler 104 Unhandled API Exception [3526b994]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,170 exceptions custom_exception_handler 104 Unhandled API Exception [b8266201]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,170 exceptions custom_exception_handler 104 Unhandled API Exception [3526b994]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,198 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-07-23 04:52:07,199 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-07-23 04:52:07,202 exceptions custom_exception_handler 104 Unhandled API Exception [7d20e62e]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,202 exceptions custom_exception_handler 104 Unhandled API Exception [7d20e62e]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,211 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-07-23 04:52:07,250 exceptions custom_exception_handler 104 Unhandled API Exception [ed8bdd27]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,250 exceptions custom_exception_handler 104 Unhandled API Exception [ed8bdd27]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-23 04:52:07,263 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-07-23 05:02:43,138 s3 upload_file 108 Failed to upload file to S3: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
ERROR 2025-07-23 05:02:43,139 upload upload_file 167 File upload failed → An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 134, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/upload.py", line 796, in _main
    client.put_object(Bucket=bucket, Key=key, Body=body, **extra_args)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
ERROR 2025-07-23 05:02:43,161 views post 123 File upload failed via service <NAME_EMAIL>: Failed to upload file: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 134, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/upload.py", line 796, in _main
    client.put_object(Bucket=bucket, Key=key, Body=body, **extra_args)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/files/views.py", line 89, in post
    upload_result = file_upload_service.upload_file(
        file_obj=file_obj,
    ...<6 lines>...
        category=category,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 168, in upload_file
    raise FileUploadError(f"Failed to upload file: {exc}")
core.exceptions.FileUploadError: Failed to upload file: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
ERROR 2025-07-23 05:02:43,174 log log_response 246 Internal Server Error: /api/v1/files/upload/
ERROR 2025-07-23 05:02:43,965 s3 upload_file 108 Failed to upload file to S3: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
ERROR 2025-07-23 05:02:43,965 upload upload_file 167 File upload failed → An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 134, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/upload.py", line 796, in _main
    client.put_object(Bucket=bucket, Key=key, Body=body, **extra_args)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
ERROR 2025-07-23 05:02:43,974 views post 123 File upload failed via service <NAME_EMAIL>: Failed to upload file: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 134, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/upload.py", line 796, in _main
    client.put_object(Bucket=bucket, Key=key, Body=body, **extra_args)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/files/views.py", line 89, in post
    upload_result = file_upload_service.upload_file(
        file_obj=file_obj,
    ...<6 lines>...
        category=category,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 168, in upload_file
    raise FileUploadError(f"Failed to upload file: {exc}")
core.exceptions.FileUploadError: Failed to upload file: An error occurred (InvalidAccessKeyId) when calling the PutObject operation: The AWS Access Key Id you provided does not exist in our records.
ERROR 2025-07-23 05:02:43,986 log log_response 246 Internal Server Error: /api/v1/files/upload/
ERROR 2025-07-23 06:29:29,099 error_handling process_exception 66 Unhandled exception [7eb687db]: UNIQUE constraint failed: business_business.slug
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: business_business.slug

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1886, in add_view
    return self.changeform_view(request, None, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1747, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1798, in _changeform_view
    self.save_model(request, new_object, form, not add)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1227, in save_model
    obj.save()
    ~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/business/models.py", line 38, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: business_business.slug
ERROR 2025-07-23 06:29:29,243 log log_response 246 Internal Server Error: /admin/business/business/add/
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: business_business.slug

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1886, in add_view
    return self.changeform_view(request, None, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1747, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1798, in _changeform_view
    self.save_model(request, new_object, form, not add)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1227, in save_model
    obj.save()
    ~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/business/models.py", line 38, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: business_business.slug
ERROR 2025-07-23 06:29:45,612 error_handling process_exception 66 Unhandled exception [80ef07c7]: no such table: business_businesssettings
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: business_businesssettings

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/paginator.py", line 93, in count
    return c()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: business_businesssettings
ERROR 2025-07-23 06:29:45,762 log log_response 246 Internal Server Error: /admin/business/businesssettings/
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: business_businesssettings

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/paginator.py", line 93, in count
    return c()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: business_businesssettings
ERROR 2025-07-23 06:30:08,438 error_handling process_exception 66 Unhandled exception [8480573a]: UNIQUE constraint failed: business_business.slug
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: business_business.slug

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1886, in add_view
    return self.changeform_view(request, None, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1747, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1798, in _changeform_view
    self.save_model(request, new_object, form, not add)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1227, in save_model
    obj.save()
    ~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/business/models.py", line 38, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: business_business.slug
ERROR 2025-07-23 06:30:08,578 log log_response 246 Internal Server Error: /admin/business/business/add/
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: business_business.slug

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1886, in add_view
    return self.changeform_view(request, None, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1747, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1798, in _changeform_view
    self.save_model(request, new_object, form, not add)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1227, in save_model
    obj.save()
    ~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/business/models.py", line 38, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: business_business.slug
ERROR 2025-07-23 06:38:33,293 error_handling process_exception 66 Unhandled exception [3f85c4c7]: UNIQUE constraint failed: business_business.slug
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: business_business.slug

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1886, in add_view
    return self.changeform_view(request, None, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1747, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1798, in _changeform_view
    self.save_model(request, new_object, form, not add)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1227, in save_model
    obj.save()
    ~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/business/models.py", line 38, in save
    if not self.name:
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: business_business.slug
ERROR 2025-07-23 06:38:33,515 log log_response 246 Internal Server Error: /admin/business/business/add/
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: business_business.slug

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1886, in add_view
    return self.changeform_view(request, None, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1747, in changeform_view
    return self._changeform_view(request, object_id, form_url, extra_context)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1798, in _changeform_view
    self.save_model(request, new_object, form, not add)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1227, in save_model
    obj.save()
    ~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/business/models.py", line 38, in save
    if not self.name:
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: business_business.slug
ERROR 2025-07-24 17:53:56,837 s3 upload_file 108 Failed to upload file to S3: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-24 17:53:56,837 upload upload_file 186 File upload failed → An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 153, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 351, in _main
    response = client.create_multipart_upload(
        Bucket=bucket, Key=key, **extra_args
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-24 17:53:56,894 views post 125 File upload failed via service <NAME_EMAIL>: Failed to upload file: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 153, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 351, in _main
    response = client.create_multipart_upload(
        Bucket=bucket, Key=key, **extra_args
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/files/views.py", line 90, in post
    upload_result = file_upload_service.upload_file(
        file_obj=file_obj,
    ...<7 lines>...
        category=category,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 187, in upload_file
    raise FileUploadError(f"Failed to upload file: {exc}")
core.exceptions.FileUploadError: Failed to upload file: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-24 17:53:56,905 log log_response 246 Internal Server Error: /api/v1/files/upload/
ERROR 2025-07-24 17:53:57,596 s3 upload_file 108 Failed to upload file to S3: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-24 17:53:57,597 upload upload_file 186 File upload failed → An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 153, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 351, in _main
    response = client.create_multipart_upload(
        Bucket=bucket, Key=key, **extra_args
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-24 17:53:57,603 views post 125 File upload failed via service <NAME_EMAIL>: Failed to upload file: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 153, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 351, in _main
    response = client.create_multipart_upload(
        Bucket=bucket, Key=key, **extra_args
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/files/views.py", line 90, in post
    upload_result = file_upload_service.upload_file(
        file_obj=file_obj,
    ...<7 lines>...
        category=category,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 187, in upload_file
    raise FileUploadError(f"Failed to upload file: {exc}")
core.exceptions.FileUploadError: Failed to upload file: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-24 17:53:57,613 log log_response 246 Internal Server Error: /api/v1/files/upload/
ERROR 2025-07-24 22:58:42,019 error_handling process_exception 66 Unhandled exception [6b48c271]: no such table: business_businesssettings
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: business_businesssettings

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/paginator.py", line 93, in count
    return c()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: business_businesssettings
ERROR 2025-07-24 22:58:42,177 log log_response 246 Internal Server Error: /admin/business/businesssettings/
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: business_businesssettings

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/views/decorators/cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/contrib/admin/views/main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/paginator.py", line 93, in count
    return c()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: business_businesssettings
ERROR 2025-07-24 23:00:37,866 log log_response 246 Internal Server Error: /api/v1/business-customers/me/
ERROR 2025-07-24 23:00:37,922 log log_response 246 Internal Server Error: /api/v1/business-customers/me/
ERROR 2025-07-24 23:00:40,427 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,427 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,448 log log_response 246 Internal Server Error: /api/v1/employees/me/
ERROR 2025-07-24 23:00:40,501 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,501 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,509 log log_response 246 Internal Server Error: /api/v1/employees/me/
ERROR 2025-07-24 23:00:40,685 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,685 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,706 log log_response 246 Internal Server Error: /api/v1/employees/me/
ERROR 2025-07-24 23:00:40,763 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,763 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,776 log log_response 246 Internal Server Error: /api/v1/employees/me/
ERROR 2025-07-24 23:00:40,835 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,835 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,848 log log_response 246 Internal Server Error: /api/v1/employees/me/
ERROR 2025-07-24 23:00:40,863 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,863 views get 37 Error fetching employee profile: No Employee matches the given query.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
employees.models.Employee.DoesNotExist: Employee matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/employees/views.py", line 31, in get
    employee = get_object_or_404(Employee, user=request.user)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/shortcuts.py", line 87, in get_object_or_404
    raise Http404(
        "No %s matches the given query." % queryset.model._meta.object_name
    )
django.http.response.Http404: No Employee matches the given query.
ERROR 2025-07-24 23:00:40,874 log log_response 246 Internal Server Error: /api/v1/employees/me/
ERROR 2025-07-24 23:00:41,179 log log_response 246 Internal Server Error: /api/v1/business-customers/me/
ERROR 2025-07-24 23:00:41,193 log log_response 246 Internal Server Error: /api/v1/business-customers/me/
ERROR 2025-07-25 05:19:15,014 upload upload_file 186 File upload failed → File size exceeds maximum allowed size of 52428800 bytes
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 68, in upload_file
    self._validate_file(file_obj)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 258, in _validate_file
    raise ValidationError(
        f"File size exceeds maximum allowed size of {self.MAX_FILE_SIZE} bytes"
    )
core.exceptions.ValidationError: File size exceeds maximum allowed size of 52428800 bytes
ERROR 2025-07-25 05:19:15,018 views post 125 File upload failed via service <NAME_EMAIL>: Failed to upload file: File size exceeds maximum allowed size of 52428800 bytes
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 68, in upload_file
    self._validate_file(file_obj)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 258, in _validate_file
    raise ValidationError(
        f"File size exceeds maximum allowed size of {self.MAX_FILE_SIZE} bytes"
    )
core.exceptions.ValidationError: File size exceeds maximum allowed size of 52428800 bytes

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/files/views.py", line 90, in post
    upload_result = file_upload_service.upload_file(
        file_obj=file_obj,
    ...<7 lines>...
        category=category,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 187, in upload_file
    raise FileUploadError(f"Failed to upload file: {exc}")
core.exceptions.FileUploadError: Failed to upload file: File size exceeds maximum allowed size of 52428800 bytes
ERROR 2025-07-25 05:19:15,026 log log_response 246 Internal Server Error: /api/v1/files/upload/
ERROR 2025-07-25 05:46:21,717 s3 upload_file 108 Failed to upload file to S3: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-25 05:46:21,717 upload upload_file 186 File upload failed → An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 153, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 351, in _main
    response = client.create_multipart_upload(
        Bucket=bucket, Key=key, **extra_args
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-25 05:46:21,735 views post 125 File upload failed via service <NAME_EMAIL>: Failed to upload file: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 153, in upload_file
    self.s3_service.upload_file(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj=BytesIO(payload),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        tags=tags,
        ^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/aws_services/s3.py", line 95, in upload_file
    self.client.upload_fileobj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        file_obj,
        ^^^^^^^^^
    ...<2 lines>...
        ExtraArgs=extra_args
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/boto3/s3/inject.py", line 675, in upload_fileobj
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 111, in result
    return self._coordinator.result()
           ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/futures.py", line 287, in result
    raise self._exception
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 142, in __call__
    return self._execute_main(kwargs)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 165, in _execute_main
    return_value = self._main(**kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/s3transfer/tasks.py", line 351, in _main
    response = client.create_multipart_upload(
        Bucket=bucket, Key=key, **extra_args
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 598, in _api_call
    return self._make_api_call(operation_name, kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/botocore/client.py", line 1061, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.exceptions.ClientError: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/files/views.py", line 90, in post
    upload_result = file_upload_service.upload_file(
        file_obj=file_obj,
    ...<7 lines>...
        category=category,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/apps/files/services/upload.py", line 187, in upload_file
    raise FileUploadError(f"Failed to upload file: {exc}")
core.exceptions.FileUploadError: Failed to upload file: An error occurred (ExpiredToken) when calling the CreateMultipartUpload operation: The provided token has expired.
ERROR 2025-07-25 05:46:21,748 log log_response 246 Internal Server Error: /api/v1/files/upload/
ERROR 2025-07-26 00:35:39,464 exceptions custom_exception_handler 104 Unhandled API Exception [568569e9]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:35:39,464 exceptions custom_exception_handler 104 Unhandled API Exception [568569e9]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:35:39,571 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:37:11,159 exceptions custom_exception_handler 104 Unhandled API Exception [b143efc3]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:37:11,159 exceptions custom_exception_handler 104 Unhandled API Exception [b143efc3]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:37:11,182 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:37:34,893 exceptions custom_exception_handler 104 Unhandled API Exception [3aaea5c7]: ValueError - Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'ad5585f4-bf28-4b50-983c-83b83aee8237'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
ERROR 2025-07-26 00:37:34,893 exceptions custom_exception_handler 104 Unhandled API Exception [3aaea5c7]: ValueError - Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'ad5585f4-bf28-4b50-983c-83b83aee8237'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
ERROR 2025-07-26 00:37:34,923 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:37:42,598 exceptions custom_exception_handler 104 Unhandled API Exception [2bc0f2ca]: ValueError - Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'ad5585f4-bf28-4b50-983c-83b83aee8237'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
ERROR 2025-07-26 00:37:42,598 exceptions custom_exception_handler 104 Unhandled API Exception [2bc0f2ca]: ValueError - Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'ad5585f4-bf28-4b50-983c-83b83aee8237'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'ad5585f4-bf28-4b50-983c-83b83aee8237'.
ERROR 2025-07-26 00:37:42,620 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:38:07,028 exceptions custom_exception_handler 104 Unhandled API Exception [52d626e4]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:38:07,028 exceptions custom_exception_handler 104 Unhandled API Exception [52d626e4]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:38:07,052 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:42:30,227 exceptions custom_exception_handler 104 Unhandled API Exception [41810af6]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:42:30,227 exceptions custom_exception_handler 104 Unhandled API Exception [41810af6]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:42:30,242 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:43:12,704 exceptions custom_exception_handler 104 Unhandled API Exception [ef222bc2]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:43:12,704 exceptions custom_exception_handler 104 Unhandled API Exception [ef222bc2]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:43:12,733 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:44:07,764 exceptions custom_exception_handler 104 Unhandled API Exception [0f2a906a]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:44:07,764 exceptions custom_exception_handler 104 Unhandled API Exception [0f2a906a]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:44:07,783 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:48:55,412 exceptions custom_exception_handler 104 Unhandled API Exception [a2fe778b]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:48:55,412 exceptions custom_exception_handler 104 Unhandled API Exception [a2fe778b]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:48:55,424 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 00:49:31,810 exceptions custom_exception_handler 104 Unhandled API Exception [bd85e5ce]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:49:31,810 exceptions custom_exception_handler 104 Unhandled API Exception [bd85e5ce]: ValueError - Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'a0c0e89f-3449-4078-a61f-85300a05c3ad'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/appointments/views.py", line 49, in get_queryset
    queryset = queryset.filter(customer_id=customer_id)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
ValueError: Field 'id' expected a number but got 'a0c0e89f-3449-4078-a61f-85300a05c3ad'.
ERROR 2025-07-26 00:49:31,840 log log_response 246 Internal Server Error: /api/v1/appointments/
ERROR 2025-07-26 01:12:52,898 error_handling process_exception 66 Unhandled exception [37ff7a66]: Object of type PhoneNumber is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/renderers.py", line 100, in render
    ret = json.dumps(
        data, cls=self.encoder_class,
        indent=indent, ensure_ascii=self.ensure_ascii,
        allow_nan=not self.strict, separators=separators
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/encoders.py", line 67, in default
    return super().default(obj)
           ~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type PhoneNumber is not JSON serializable
ERROR 2025-07-26 01:12:52,907 log log_response 246 Internal Server Error: /api/v1/auth/user/
ERROR 2025-07-26 01:12:52,931 error_handling process_exception 66 Unhandled exception [dcecf5ac]: Object of type PhoneNumber is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/renderers.py", line 100, in render
    ret = json.dumps(
        data, cls=self.encoder_class,
        indent=indent, ensure_ascii=self.ensure_ascii,
        allow_nan=not self.strict, separators=separators
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/encoders.py", line 67, in default
    return super().default(obj)
           ~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type PhoneNumber is not JSON serializable
ERROR 2025-07-26 01:12:52,940 log log_response 246 Internal Server Error: /api/v1/auth/user/
ERROR 2025-07-26 01:13:40,336 error_handling process_exception 66 Unhandled exception [1ce387ab]: Object of type PhoneNumber is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/renderers.py", line 100, in render
    ret = json.dumps(
        data, cls=self.encoder_class,
        indent=indent, ensure_ascii=self.ensure_ascii,
        allow_nan=not self.strict, separators=separators
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/encoders.py", line 67, in default
    return super().default(obj)
           ~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type PhoneNumber is not JSON serializable
ERROR 2025-07-26 01:13:40,345 log log_response 246 Internal Server Error: /api/v1/auth/user/
ERROR 2025-07-26 01:13:40,365 error_handling process_exception 66 Unhandled exception [cff25fe7]: Object of type PhoneNumber is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/renderers.py", line 100, in render
    ret = json.dumps(
        data, cls=self.encoder_class,
        indent=indent, ensure_ascii=self.ensure_ascii,
        allow_nan=not self.strict, separators=separators
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/json.py", line 25, in dumps
    return json.dumps(*args, **kwargs)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/utils/encoders.py", line 67, in default
    return super().default(obj)
           ~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type PhoneNumber is not JSON serializable
ERROR 2025-07-26 01:13:40,373 log log_response 246 Internal Server Error: /api/v1/auth/user/
ERROR 2025-07-27 01:49:27,541 exceptions custom_exception_handler 104 Unhandled API Exception [efa273df]: OperationalError - table forms_formsubmission has no column named customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named customer_id
ERROR 2025-07-27 01:49:27,541 exceptions custom_exception_handler 104 Unhandled API Exception [efa273df]: OperationalError - table forms_formsubmission has no column named customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named customer_id
ERROR 2025-07-27 01:49:27,631 log log_response 246 Internal Server Error: /api/v1/forms/submissions/
ERROR 2025-07-27 03:01:11,023 exceptions custom_exception_handler 104 Unhandled API Exception [7c42801b]: OperationalError - table forms_formsubmission has no column named business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/serializers.py", line 97, in create
    return super().create(validated_data)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named business_customer_id
ERROR 2025-07-27 03:01:11,023 exceptions custom_exception_handler 104 Unhandled API Exception [7c42801b]: OperationalError - table forms_formsubmission has no column named business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/serializers.py", line 97, in create
    return super().create(validated_data)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named business_customer_id
ERROR 2025-07-27 03:01:11,054 log log_response 246 Internal Server Error: /api/v1/forms/submissions/
ERROR 2025-07-27 03:34:13,756 exceptions custom_exception_handler 104 Unhandled API Exception [756609d5]: OperationalError - table forms_formsubmission has no column named customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named customer_id
ERROR 2025-07-27 03:34:13,756 exceptions custom_exception_handler 104 Unhandled API Exception [756609d5]: OperationalError - table forms_formsubmission has no column named customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named customer_id
ERROR 2025-07-27 03:34:13,788 log log_response 246 Internal Server Error: /api/v1/forms/submissions/
ERROR 2025-07-27 05:17:09,875 exceptions custom_exception_handler 104 Unhandled API Exception [c533c710]: OperationalError - no such column: customers_customerprofile.consent_signed
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 473, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'customer_profile'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: customers_customerprofile.consent_signed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/auth/views.py", line 76, in post
    if hasattr(user, 'customer_profile'):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 481, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 633, in get
    num = len(clone)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: customers_customerprofile.consent_signed
ERROR 2025-07-27 05:17:09,875 exceptions custom_exception_handler 104 Unhandled API Exception [c533c710]: OperationalError - no such column: customers_customerprofile.consent_signed
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 473, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'customer_profile'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: customers_customerprofile.consent_signed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/auth/views.py", line 76, in post
    if hasattr(user, 'customer_profile'):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 481, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 633, in get
    num = len(clone)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: customers_customerprofile.consent_signed
ERROR 2025-07-27 05:17:09,929 log log_response 246 Internal Server Error: /api/v1/auth/login/
ERROR 2025-07-27 05:17:14,088 exceptions custom_exception_handler 104 Unhandled API Exception [a550d8c1]: OperationalError - no such column: customers_customerprofile.consent_signed
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 473, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'customer_profile'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: customers_customerprofile.consent_signed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/auth/views.py", line 76, in post
    if hasattr(user, 'customer_profile'):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 481, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 633, in get
    num = len(clone)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: customers_customerprofile.consent_signed
ERROR 2025-07-27 05:17:14,088 exceptions custom_exception_handler 104 Unhandled API Exception [a550d8c1]: OperationalError - no such column: customers_customerprofile.consent_signed
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 473, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'customer_profile'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: customers_customerprofile.consent_signed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/auth/views.py", line 76, in post
    if hasattr(user, 'customer_profile'):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/related_descriptors.py", line 481, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 633, in get
    num = len(clone)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: customers_customerprofile.consent_signed
ERROR 2025-07-27 05:17:14,101 log log_response 246 Internal Server Error: /api/v1/auth/login/
ERROR 2025-07-27 07:14:09,297 exceptions custom_exception_handler 104 Unhandled API Exception [b77272ba]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:14:09,297 exceptions custom_exception_handler 104 Unhandled API Exception [b77272ba]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:14:09,409 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:14:28,642 exceptions custom_exception_handler 104 Unhandled API Exception [03f989f9]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:14:28,642 exceptions custom_exception_handler 104 Unhandled API Exception [03f989f9]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:14:28,658 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:15:18,866 exceptions custom_exception_handler 104 Unhandled API Exception [e233273d]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:15:18,866 exceptions custom_exception_handler 104 Unhandled API Exception [e233273d]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:15:18,894 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:22:59,457 exceptions custom_exception_handler 104 Unhandled API Exception [7f1dabe8]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:22:59,457 exceptions custom_exception_handler 104 Unhandled API Exception [7f1dabe8]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:22:59,474 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:23:34,843 exceptions custom_exception_handler 104 Unhandled API Exception [851930e8]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:23:34,843 exceptions custom_exception_handler 104 Unhandled API Exception [851930e8]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:23:34,869 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:25:59,649 exceptions custom_exception_handler 104 Unhandled API Exception [55f57ede]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:25:59,649 exceptions custom_exception_handler 104 Unhandled API Exception [55f57ede]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:25:59,676 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:27:17,276 exceptions custom_exception_handler 104 Unhandled API Exception [1c061247]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:27:17,276 exceptions custom_exception_handler 104 Unhandled API Exception [1c061247]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:27:17,309 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:27:22,405 exceptions custom_exception_handler 104 Unhandled API Exception [5e07c710]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:27:22,405 exceptions custom_exception_handler 104 Unhandled API Exception [5e07c710]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:27:22,421 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:28:01,792 exceptions custom_exception_handler 104 Unhandled API Exception [ab98f3ef]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:28:01,792 exceptions custom_exception_handler 104 Unhandled API Exception [ab98f3ef]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:28:01,820 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 07:28:35,220 exceptions custom_exception_handler 104 Unhandled API Exception [b35c2b37]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:28:35,220 exceptions custom_exception_handler 104 Unhandled API Exception [b35c2b37]: OperationalError - no such column: forms_signature.business_customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: forms_signature.business_customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/forms/views.py", line 259, in latest_for_me
    latest_signature = queryset.order_by('-created_at').first()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1057, in first
    for obj in queryset[:1]:
               ~~~~~~~~^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: forms_signature.business_customer_id
ERROR 2025-07-27 07:28:35,249 log log_response 246 Internal Server Error: /api/v1/forms/signatures/me/
ERROR 2025-07-27 17:34:14,856 exceptions custom_exception_handler 104 Unhandled API Exception [98730832]: OperationalError - table forms_formsubmission has no column named customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named customer_id
ERROR 2025-07-27 17:34:14,856 exceptions custom_exception_handler 104 Unhandled API Exception [98730832]: OperationalError - table forms_formsubmission has no column named customer_id
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: table forms_formsubmission has no column named customer_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/mixins.py", line 24, in perform_create
    serializer.save()
    ~~~~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: table forms_formsubmission has no column named customer_id
ERROR 2025-07-27 17:34:14,952 log log_response 246 Internal Server Error: /api/v1/forms/submissions/
ERROR 2025-07-27 20:37:38,943 exception response_for_exception 124 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-27 20:37:49,467 exception response_for_exception 124 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-27 21:24:48,072 exceptions custom_exception_handler 104 Unhandled API Exception [96214cd9]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:48,072 exceptions custom_exception_handler 104 Unhandled API Exception [96214cd9]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:48,187 log log_response 246 Internal Server Error: /api/v1/booking-sessions/update-data/
ERROR 2025-07-27 21:24:49,076 exceptions custom_exception_handler 104 Unhandled API Exception [b3fd69eb]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:49,076 exceptions custom_exception_handler 104 Unhandled API Exception [b3fd69eb]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:49,094 log log_response 246 Internal Server Error: /api/v1/booking-sessions/update-data/
ERROR 2025-07-27 21:24:50,171 exceptions custom_exception_handler 104 Unhandled API Exception [45fb098e]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:50,171 exceptions custom_exception_handler 104 Unhandled API Exception [45fb098e]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:50,194 log log_response 246 Internal Server Error: /api/v1/booking-sessions/update-data/
ERROR 2025-07-27 21:24:51,481 exceptions custom_exception_handler 104 Unhandled API Exception [214a053b]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:51,481 exceptions custom_exception_handler 104 Unhandled API Exception [214a053b]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:24:51,503 log log_response 246 Internal Server Error: /api/v1/booking-sessions/update-data/
ERROR 2025-07-27 21:25:16,298 exceptions custom_exception_handler 104 Unhandled API Exception [5bccc86d]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:25:16,298 exceptions custom_exception_handler 104 Unhandled API Exception [5bccc86d]: TypeError - Object of type Decimal is not JSON serializable
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/api/v1/customers/views.py", line 636, in update_booking_data
    session.save()
    ~~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/customers/models.py", line 249, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 990, in _save_table
    updated = self._do_update(
        base_qs, using, pk_val, values, update_fields, forced_update
    )
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/base.py", line 1054, in _do_update
    return filtered._update(values) > 0
           ~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 1231, in _update
    return query.get_compiler(self.db).execute_sql(CURSOR)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1947, in as_sql
    val = field.get_db_prep_save(val, connection=self.connection)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 136, in get_db_prep_save
    return self.get_db_prep_value(value, connection)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/fields/json.py", line 131, in get_db_prep_value
    return connection.ops.adapt_json_value(value, self.encoder)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/backends/base/operations.py", line 584, in adapt_json_value
    return json.dumps(value, cls=encoder)
           ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Decimal is not JSON serializable
ERROR 2025-07-27 21:25:16,314 log log_response 246 Internal Server Error: /api/v1/booking-sessions/update-data/
ERROR 2025-08-01 23:42:53,059 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-01 23:42:53,059 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-01 23:42:53,060 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 00:12:06,154 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 00:12:06,154 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 00:12:06,154 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 00:26:08,699 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,699 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,702 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,702 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,705 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,705 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,708 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:26:08,708 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:20,993 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:20,993 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:20,994 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:20,994 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:20,998 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:20,998 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:21,000 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:28:21,000 authentication get_validated_token 57 Unexpected error during token validation: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:29:25,755 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 00:29:25,755 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 00:29:25,757 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 00:31:14,833 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 00:31:14,833 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 00:31:14,834 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 00:58:00,958 authentication authenticate 51 Unexpected authentication error: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:58:00,958 authentication authenticate 51 Unexpected authentication error: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:58:00,962 authentication authenticate 51 Unexpected authentication error: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 00:58:00,962 authentication authenticate 51 Unexpected authentication error: {'detail': ErrorDetail(string='Given token not valid for any token type', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid'), 'messages': [{'token_class': ErrorDetail(string='AccessToken', code='token_not_valid'), 'token_type': ErrorDetail(string='access', code='token_not_valid'), 'message': ErrorDetail(string='Token is invalid', code='token_not_valid')}]}
ERROR 2025-08-02 01:36:30,897 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:36:30,897 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:36:30,898 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:36:35,479 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:36:35,479 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:36:35,480 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:32,985 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:32,985 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:32,986 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:32,998 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:32,998 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:32,999 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:43,631 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:43,631 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:43,632 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:43,643 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:43,643 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:43,644 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:44,190 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,190 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,191 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:44,203 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,203 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,204 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:44,845 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,845 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,847 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:44,862 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,862 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:44,863 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:45,208 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,208 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,210 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:45,223 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,223 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,224 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:45,581 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,581 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,584 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:45,596 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,596 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,598 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:45,808 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,808 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,810 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:45,824 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,824 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:45,825 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:46,144 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,144 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,145 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:46,159 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,159 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,160 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:46,460 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,460 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,462 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:40:46,476 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,476 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:40:46,477 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:48:26,187 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:26,187 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:26,188 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:48:28,163 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:28,163 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:28,164 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:48:28,176 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:28,176 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:28,177 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:48:33,542 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:33,542 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:33,546 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:48:33,567 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:33,567 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:48:33,568 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:51:10,314 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:51:10,314 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:51:10,315 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:51:10,327 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:51:10,327 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:51:10,328 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:57:02,266 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:57:02,266 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:57:02,267 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-02 01:57:02,276 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:57:02,276 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-02 01:57:02,277 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:43:11,232 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:43:11,232 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:43:11,233 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:44:41,836 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:41,836 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:41,837 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:44:44,643 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:44,643 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:44,644 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:44:45,784 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:45,784 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:45,784 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:44:46,724 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:46,724 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:44:46,725 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:46:18,842 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:46:18,842 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:46:18,843 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:46:33,663 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:46:33,663 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:46:33,664 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:47:15,320 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:15,320 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:15,322 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:47:55,858 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:55,858 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:55,859 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:47:57,738 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:57,738 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:57,740 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:47:58,397 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:58,397 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:58,398 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:47:58,947 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:58,947 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:47:58,948 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:48:00,671 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:48:00,671 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:48:00,672 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:48:28,803 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:48:28,803 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:48:28,804 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 00:48:37,546 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:48:37,546 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 00:48:37,547 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 01:48:29,610 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 01:48:29,610 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 01:48:29,611 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 03:36:18,147 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 03:36:18,147 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-03 03:36:18,148 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-03 03:40:42,023 exceptions custom_exception_handler 127 Unhandled API Exception [262a5eb6]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,024 exceptions custom_exception_handler 127 Unhandled API Exception [7e033606]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,044 exceptions custom_exception_handler 127 Unhandled API Exception [59dfbba8]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,023 exceptions custom_exception_handler 127 Unhandled API Exception [262a5eb6]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,024 exceptions custom_exception_handler 127 Unhandled API Exception [7e033606]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,063 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-08-03 03:40:42,044 exceptions custom_exception_handler 127 Unhandled API Exception [59dfbba8]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,063 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-08-03 03:40:42,064 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-08-03 03:40:42,118 exceptions custom_exception_handler 127 Unhandled API Exception [76ed23bc]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,118 exceptions custom_exception_handler 127 Unhandled API Exception [76ed23bc]: DoesNotExist - User matching query does not exist.
Traceback (most recent call last):
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 116, in validate
    user := get_user_model().objects.get(
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        **{api_settings.USER_ID_FIELD: user_id}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/mnt/c/ProjectWork/chatbook-backend/venv/lib/python3.13/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
        "%s matching query does not exist." % self.model._meta.object_name
    )
accounts.models.user.User.DoesNotExist: User matching query does not exist.
ERROR 2025-08-03 03:40:42,128 log log_response 246 Internal Server Error: /api/v1/auth/refresh/
ERROR 2025-08-06 22:38:46,862 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:38:46,862 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:38:46,864 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:39:04,564 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:04,564 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:04,566 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:39:46,315 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:46,315 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:46,316 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:39:47,680 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:47,680 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:47,681 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:39:48,804 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:48,804 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:48,805 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:39:49,533 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:49,533 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:39:49,534 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:42:34,455 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:34,455 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:34,456 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:42:35,390 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:35,390 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:35,392 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:42:36,172 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:36,172 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:36,174 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:42:36,779 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:36,779 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:42:36,780 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:45:21,845 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:21,845 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:21,846 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:45:24,388 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:24,388 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:24,389 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:45:30,137 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:30,137 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:30,139 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:45:32,545 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:32,545 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:45:32,546 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:46:15,836 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:46:15,836 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:46:15,837 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:46:18,299 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:46:18,299 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:46:18,300 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 22:59:31,373 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:59:31,373 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 22:59:31,375 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:00:01,129 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:00:01,129 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:00:01,130 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:00:55,933 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:00:55,933 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:00:55,934 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:01:14,433 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:14,433 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:14,434 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:01:16,916 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:16,916 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:16,917 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:01:18,342 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:18,342 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:18,343 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:01:18,673 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:18,673 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:18,674 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:01:18,980 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:18,980 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:18,982 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:01:19,362 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:19,362 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:01:19,363 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:04:09,665 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:04:09,665 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:04:09,666 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:04:12,166 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:04:12,166 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:04:12,167 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:56,823 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:56,823 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:56,824 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:57,557 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:57,557 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:57,558 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:58,176 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:58,176 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:58,177 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:58,688 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:58,688 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:58,689 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:59,174 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:59,174 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:59,175 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:59,604 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:59,604 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:59,605 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:09:59,938 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:59,938 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:09:59,939 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:00,233 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:00,233 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:00,234 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:00,663 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:00,663 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:00,664 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:01,117 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:01,117 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:01,119 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:01,580 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:01,580 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:01,581 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:34,108 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:34,108 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:34,109 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:42,999 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:42,999 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:43,000 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:44,112 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:44,112 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:44,113 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:44,785 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:44,785 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:44,786 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:10:45,109 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:45,109 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:10:45,110 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:12:35,472 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:12:35,472 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:12:35,473 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:13:06,000 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:06,000 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:06,001 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:13:08,270 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:08,270 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:08,271 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:13:11,251 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:11,251 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:11,252 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:13:15,032 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:15,032 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:13:15,034 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:16:08,808 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:16:08,808 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:16:08,809 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-06 23:31:33,700 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:31:33,700 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-06 23:31:33,702 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-07 01:08:39,678 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-07 01:08:39,678 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-07 01:08:39,679 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-07 01:08:41,329 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-07 01:08:41,329 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-07 01:08:41,330 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
ERROR 2025-08-07 01:47:25,836 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-07 01:47:25,836 views get 1256 Error checking customer form completion: No CustomerProfile matches the given query.
ERROR 2025-08-07 01:47:25,837 log log_response 246 Internal Server Error: /api/v1/business-customers/me/forms/
